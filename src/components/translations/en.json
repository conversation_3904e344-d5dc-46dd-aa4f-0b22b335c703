{"actions.apply": "Apply", "actions.buy": "Buy", "actions.cancel": "Cancel", "actions.close": "Close", "actions.confirm": "Confirm", "actions.delete": "Delete", "actions.edit": "Edit", "actions.fulfill": "Fulfill", "actions.pause": "Pause", "actions.reject": "Reject", "actions.resend": "Resend", "actions.save": "Save", "actions.saved": "Saved", "actions.send": "Send", "actions.sent": "<PERSON><PERSON>", "actions.signIn": "Sign in", "actions.signOut": "Sign out", "actions.signUp": "Sign up", "actions.submit": "Submit", "attachGiftToOrderDrawer.activateOrderTitle": "How to activate your order:", "attachGiftToOrderDrawer.activateStep1": "Deposit gift to bot", "attachGiftToOrderDrawer.activateStep2": "Go to the bot", "attachGiftToOrderDrawer.activateStep3": "Press \"My Sell Orders\" waiting activation", "attachGiftToOrderDrawer.activateStep4": "Select order to activate", "attachGiftToOrderDrawer.activateStep5": "Send gift to relayer to activate", "attachGiftToOrderDrawer.errorLinkingGift": "Error linking gift to order", "attachGiftToOrderDrawer.errorLoadingGifts": "Error loading available gifts", "attachGiftToOrderDrawer.giftLinkedSuccessfully": "Gift linked to order successfully", "attachGiftToOrderDrawer.instructionStep1": "Send the gift directly to the relayer, then return to the app and attach the corresponding gift to your order", "attachGiftToOrderDrawer.instructionStep2": "2. Click \"Attach Gift to this Order\" button and select the deposited gift", "attachGiftToOrderDrawer.instructionsTitle": "How to activate your order:", "attachGiftToOrderDrawer.linkGiftToOrder": "Link Gift to Order", "attachGiftToOrderDrawer.linking": "Linking...", "attachGiftToOrderDrawer.noGiftsAvailable": "No gifts available for this collection", "attachGiftToOrderDrawer.selectGift": "Select a gift to attach", "attachGiftToOrderDrawer.title": "Attach Gift to Order", "collection.status.deleted": "Deleted", "collection.status.market": "Market", "collection.status.premarket": "Pre-market", "collectionName.unknownCollection": "Unknown Collection", "collectionSelect.collection": "Collection", "collectionSelect.noCollectionsFound": "No collections found matching \"{searchQuery}\".", "collectionSelect.searchCollections": "Search collections...", "collectionSelect.selectCollection": "Select collection...", "common.failedToShare": "Failed to share", "common.failedToShareReferralLink": "Failed to share referral link", "common.linkCopiedToClipboard": "Link copied to clipboard", "common.referralLinkCopiedToClipboard": "Referral link copied to clipboard", "confirmWrapper.defaultMessage": "Are you sure you want to proceed with the action?", "confirmWrapper.no": "No", "confirmWrapper.yes": "Yes", "countdownPopup.closeNotification": "Close notification", "countdownPopup.depositProcessing": "Deposit Processing", "countdownPopup.minutes": "minutes", "countdownPopup.youWillReceiveFundsWithin": "You will receive your funds within", "createSellOrderDrawer.cancel": "Cancel", "createSellOrderDrawer.createOrder": "Create Order", "createSellOrderDrawer.createSellOrder": "<PERSON>ll", "createSellOrderDrawer.creating": "Creating...", "createSellOrderDrawer.priceLabel": "Price (TON)", "createSellOrderDrawer.setPrice": "Set a price for your {giftName} gift", "createSellOrderDrawer.successMessage": "Sell order created successfully!", "depositDrawer.actions.cancel": "Cancel", "depositDrawer.actions.deposit": "<PERSON><PERSON><PERSON><PERSON>", "depositDrawer.actions.pleaseConnectWallet": "Please connect your wallet to make a deposit", "depositDrawer.actions.processing": "Processing...", "depositDrawer.addTonToBalance": "Add TON to your marketplace balance", "depositDrawer.amountInput.amountMustBeAtLeast": "Amount must be at least {amount} TON", "depositDrawer.amountInput.depositAmountTon": "<PERSON><PERSON><PERSON><PERSON> (TON)", "depositDrawer.amountInput.minTonPlaceholder": "Min {amount} TON", "depositDrawer.close": "Close", "depositDrawer.copyTransactionHash": "Copy Transaction Hash", "depositDrawer.depositCompleted": "Your deposit has been completed successfully", "depositDrawer.depositFee": "Deposit fee:", "depositDrawer.depositFunds": "Deposit Funds", "depositDrawer.depositInformation": "Deposit Information", "depositDrawer.depositProcessing": "Deposit Processing", "depositDrawer.depositSuccess": "Deposit Successful!", "depositDrawer.loadingConfiguration": "Loading configuration...", "depositDrawer.minimumDeposit": "Minimum deposit:", "depositDrawer.processingYourDeposit": "Processing your deposit...", "depositDrawer.summary.depositAmount": "Deposit amount:", "depositDrawer.summary.depositFee": "Deposit fee:", "depositDrawer.summary.totalToPay": "Total to pay:", "depositDrawer.transactionHashCopied": "Transaction hash copied to clipboard", "depositDrawer.viewOnTonScan": "View on TON Scan", "depositDrawer.youWillReceiveFundsWithin": "You will receive your funds within", "errorPage.tryAgain": "Try again", "errorPage.unhandledErrorOccurred": "An unhandled error occurred!", "errors.auth.adminOnly": "Only admin users can perform this operation.", "errors.auth.permissionDenied": "Permission denied.", "errors.auth.permissionDeniedWithOperation": "You can only perform {operation} for yourself.", "errors.auth.tonWalletRequired": "User does not have a TON wallet address configured.", "errors.auth.unauthenticated": "Authentication required.", "errors.auth.userNotFound": "User not found.", "errors.balance.insufficientLockedFunds": "Insufficient locked funds for this operation.", "errors.balance.insufficientLockedFundsToSpend": "Insufficient locked funds to complete this transaction.", "errors.balance.insufficientLockedFundsToUnlock": "Insufficient locked funds to unlock the requested amount.", "errors.fulfillAndResell.insufficientBalance": "Insufficient balance to create resell order.", "errors.fulfillAndResell.invalidOrderStatus": "Order must have status \"gift sent to relayer\" to be resold.", "errors.fulfillAndResell.invalidParameters": "Order ID and resell price are required and must be valid.", "errors.fulfillAndResell.notOrderBuyer": "You are not the buyer of this order.", "errors.fulfillAndResell.orderNotFound": "Order not found.", "errors.generic.authenticationFailed": "Authentication failed.", "errors.generic.operationFailed": "Operation failed. Please try again.", "errors.generic.serverError": "Server error occurred.", "errors.generic.unknownError": "An unknown error occurred.", "errors.gift.giftAlreadyLinked": "Gift is already linked to another order.", "errors.gift.giftCollectionMismatch": "Gift and order must be from the same collection.", "errors.gift.giftInvalidStatus": "Gift must have status \"deposited\" to be linked.", "errors.gift.giftNotFound": "Gift not found.", "errors.gift.giftNotOwnedByUser": "Gift does not belong to the current user.", "errors.order.buyerCannotPurchaseSameOrder": "You cannot purchase the same order again.", "errors.order.buyersCannotCreateMarketOrders": "Buyers cannot create orders for market collections. Only sellers can create orders for market collections.", "errors.order.collectionNotActive": "Collection is not active.", "errors.order.collectionNotFound": "Collection not found.", "errors.order.insufficientBalance": "Insufficient balance.", "errors.order.onlyBuyerCanSetSecondaryPrice": "Only the current buyer can set secondary market price.", "errors.order.onlyPaidOrdersPurchasable": "Only orders with PAID status can be purchased on secondary market.", "errors.order.onlyPaidOrdersSecondaryMarket": "Only orders with PAID status can be listed on secondary market.", "errors.order.orderMustBeGiftSentStatus": "Order must be in gift sent to relayer status to complete purchase.", "errors.order.orderMustBePaidStatus": "Order must be in paid status to send gift to relayer.", "errors.order.orderMustHaveBuyerAndSeller": "Order must have both buyer and seller to be listed on secondary market.", "errors.order.orderNotAvailableSecondaryMarket": "Order is not available on secondary market.", "errors.order.orderNotFound": "Order not found.", "errors.order.secondaryPriceBelowMinimum": "Secondary market price must be at least {minPrice} TON.", "errors.order.secondaryPriceExceedsCollateral": "Secondary market price cannot exceed total collateral of {totalCollateral} TON (buyer: {buyerAmount} TON + seller: {sellerAmount} TON).", "errors.order.sellerCannotPurchaseOwnOrder": "Seller cannot purchase their own order on secondary market.", "errors.order.tooManyCreatedOrders": "You already have 3 orders that need to be activated. Please activate existing orders before creating new ones.", "errors.proposal.failedToAccept": "Failed to accept proposal. Please try again.", "errors.proposal.failedToCancel": "Failed to cancel proposal. Please try again.", "errors.proposal.failedToPropose": "Failed to create proposal. Please try again.", "errors.proposal.invalidArguments": "Invalid proposal parameters provided.", "errors.telegram.botTokenNotConfigured": "Telegram bot token not configured.", "errors.telegram.firebaseAuthError": "Firebase Auth error occurred.", "errors.telegram.iamPermissionError": "Firebase service account lacks required IAM permissions for custom token creation.", "errors.telegram.initDataRequired": "initData is required.", "errors.telegram.invalidTelegramData": "Invalid Telegram data.", "errors.validation.botTokenRequired": "Bot token is required.", "errors.validation.invalidBotToken": "Invalid bot token.", "errors.validation.invalidCollectionId": "Valid collection ID is required.", "errors.validation.invalidOrderId": "Valid order ID is required.", "errors.validation.invalidPrice": "Valid price is required.", "errors.validation.invalidSecondaryMarketPrice": "Valid secondary market price is required.", "errors.validation.ownedGiftIdRequired": "Owned gift ID is required.", "errors.validation.positiveAmountRequired": "{fieldName} must be greater than 0.", "errors.validation.requiredField": "{field} is required.", "errors.validation.userIdOrTgIdRequired": "Either userId or tgId is required.", "errors.withdrawal.amountAboveMaximum": "Withdrawal amount cannot exceed {maxAmount} TON.", "errors.withdrawal.amountBelowMinimum": "Withdrawal amount must be at least {minAmount} TON.", "errors.withdrawal.amountExceeds24hLimit": "Withdrawal amount exceeds 24-hour limit. You can withdraw up to {remainingLimit} TON. Limit resets at {resetAt}.", "errors.withdrawal.amountTooSmallAfterFees": "Amount too small after fees.", "errors.withdrawal.calculationFailed": "Failed to calculate withdrawal status", "errors.withdrawal.insufficientAvailableBalance": "Insufficient available balance for withdrawal.", "footer.marketplace": "Marketplace", "footer.myGifts": "My Gifts", "footer.myOrders": "My Orders", "footer.myProfile": "My Profile", "freezePeriodStatus.expired": "Expired", "freezePeriodStatus.freezePeriodEnded": "Freeze period has ended", "freezePeriodStatus.freezePeriodNotStarted": "Freeze period hasn't started yet", "freezePeriodStatus.timeRemaining": "{days}d {hours}h {minutes}m {seconds}s remaining", "fulfillAndResellDrawer.availableBalance": "Available balance:", "fulfillAndResellDrawer.cancel": "Cancel", "fulfillAndResellDrawer.fulfillAndResell": "Fulfill & Resell", "fulfillAndResellDrawer.insufficientBalance": "Insufficient balance to lock {amount} TON", "fulfillAndResellDrawer.lockAmount": "Lock amount:", "fulfillAndResellDrawer.lockPercentage": "Lock percentage:", "fulfillAndResellDrawer.processing": "Processing...", "fulfillAndResellDrawer.resellInformation": "Resell Order Information", "fulfillAndResellDrawer.resellPrice": "Resell Price (TON)", "fulfillAndResellDrawer.successMessage": "Order fulfilled and resell order created successfully!", "fulfillAndResellDrawer.title": "Fulfill & Resell Order", "giftInfoDrawer.claimGiftSteps": "Follow these steps to claim your gift from the relayer", "giftInfoDrawer.claimYourGift": "Claim Your Gift", "giftInfoDrawer.pressMyBuyOrders": "Press on \"My Buy Orders\" button", "giftInfoDrawer.selectYourOrder": "Select your order you want to get", "giftInfoDrawer.sendGiftSteps": "Follow these steps to send your gift to the relayer", "giftInfoDrawer.sendGiftToRelayer": "Send Gift to <PERSON><PERSON>", "gifts.clickLoginToSeeGifts": "Click on login Telegram button to see your gifts", "gifts.myGifts": "My Gifts", "gifts.noGiftsFound": "No gifts found", "gifts.orderInfo": "Order Info", "gifts.sellGift": "<PERSON><PERSON>", "gifts.youAreNotLoggedIn": "You are not logged in", "giftsPage.attachToOrder": "Attach to Order", "giftsPage.createSellOrder": "<PERSON>ll", "giftsPage.withdrawGift": "Withdraw", "header.deposit": "<PERSON><PERSON><PERSON><PERSON>", "header.profile": "Profile", "header.walletDisconnected": "Wallet disconnected", "header.withdraw": "Withdraw", "insufficientBalance.topUp": "Top up balance", "linkGiftToOrderDrawer.cancel": "Cancel", "linkGiftToOrderDrawer.linkGiftToOrder": "Link Gift to Order", "linkGiftToOrderDrawer.linking": "Linking...", "linkGiftToOrderDrawer.noEligibleOrders": "No eligible orders found. You need orders with status \"Created\" or \"Paid\" where you are the seller.", "linkGiftToOrderDrawer.selectOrderToLink": "Select an order from {collectionName} collection to link this gift", "loginModal.authenticationRequired": "Authentication Required", "loginModal.mustBeLoggedIn": "You must be logged in to perform this action.", "loginModal.signInWithTelegram": "Sign in with Telegram", "loginModal.signingIn": "Signing in...", "marketplace.activity.executedOrdersDescription": "Executed orders will be displayed here", "marketplace.activity.noActivityFound": "No activity found", "marketplace.activity.orderNumber": "Order #{number}", "marketplace.activity.viewOrder": "View order", "marketplace.createOrder.availableBalance": "Available balance:", "marketplace.createOrder.backdrop": "Backdrop:", "marketplace.createOrder.buyOrderSubtitle": "Place a buy order for an item", "marketplace.createOrder.cancel": "Cancel", "marketplace.createOrder.collection": "Collection", "marketplace.createOrder.create": "Create", "marketplace.createOrder.createBuyOrder": "Create Buy Order", "marketplace.createOrder.createSellOrder": "<PERSON>ll", "marketplace.createOrder.creating": "Creating...", "marketplace.createOrder.enterPrice": "Enter price", "marketplace.createOrder.failedToLoadGifts": "Failed to load available gifts", "marketplace.createOrder.fillRequiredFields": "Please fill in all required fields", "marketplace.createOrder.insufficientAvailableBalance": "Insufficient available balance", "marketplace.createOrder.insufficientBalance": "Insufficient balance", "marketplace.createOrder.insufficientBalanceMessage": "Insufficient balance to lock {amount} TON", "marketplace.createOrder.itemPriceLabel": "<PERSON><PERSON> (TON)", "marketplace.createOrder.loadingAvailableGifts": "Loading available gifts...", "marketplace.createOrder.loadingConfiguration": "Loading configuration...", "marketplace.createOrder.lockPercentage": "Lock percentage:", "marketplace.createOrder.marketCollectionDescription": "After creating your order, you'll need to send your gift to the relayer to activate this order.", "marketplace.createOrder.marketCollectionNotice": "Market Collection Notice", "marketplace.createOrder.maximumPrice": "Maximum price is {amount} TON", "marketplace.createOrder.minimumPrice": "Min {amount} TON", "marketplace.createOrder.noGiftsAvailable": "No gifts available for this collection. Please deposit a gift to the bot first.", "marketplace.createOrder.orderCreatedSuccess": "Order created successfully!", "marketplace.createOrder.orderInformation": "Order Information", "marketplace.createOrder.price": "Price", "marketplace.createOrder.priceFloorError": "Price must be at least {amount} TON (floor price)", "marketplace.createOrder.selectCollection": "Select a collection", "marketplace.createOrder.selectGift": "Select Gift", "marketplace.createOrder.selectGiftButton": "Select Gift ({count} available)", "marketplace.createOrder.selectGiftRequired": "Please select a gift to attach to this order", "marketplace.createOrder.selectGiftSubtitle": "Choose a gift to attach to your order", "marketplace.createOrder.selectGiftToAttach": "Select Gift to <PERSON><PERSON><PERSON> (Optional)", "marketplace.createOrder.selectedGift": "Selected: {giftName} #{giftSymbol}", "marketplace.createOrder.sellOrderSubtitle": "List your item for sale", "marketplace.resell.cancel": "Cancel", "marketplace.resell.createResaleOrder": "Create Resale Order", "marketplace.resell.failedToCreateOrder": "Failed to create secondary market order", "marketplace.resell.importantNotice": "Important Notice", "marketplace.resell.importantNoticeDescription": "Once you set a resale price, your order will be listed on the secondary market. Other users will be able to purchase it at your set price.", "marketplace.resell.loadingYourOrders": "Loading your orders...", "marketplace.resell.maximumCollateral": "Maximum: {amount} TON (collateral sum)", "marketplace.resell.minimumPrice": "Minimum: {minPrice} TON", "marketplace.resell.minimumTonPlaceholder": "Minimum {minPrice} TON", "marketplace.resell.noOrdersFoundToResell": "No orders found that can be resold", "marketplace.resell.originalPrice": "Original Price", "marketplace.resell.priceTooHighDescription": "Resale price cannot exceed the sum of locked collaterals ({amount} TON).", "marketplace.resell.priceTooHighTitle": "Price Too High", "marketplace.resell.resalePriceTon": "Resale Price (TON)", "marketplace.resell.resellMyOrder": "Resell My Order", "marketplace.resell.selectOrderToResell": "Select an order you purchased to resell on the secondary market", "marketplace.resell.setResalePrice": "Set Resale Price", "marketplace.resell.setResalePriceButton": "Set Resale Price", "marketplace.resell.setResalePriceSubtitle": "Set your price for reselling this order on the secondary market", "marketplace.resell.settingPrice": "Setting Price...", "marketplace.resell.successMessage": "Secondary market order created successfully!", "marketplace.resell.tooHigh": "✗ Too high", "marketplace.resell.tooLow": "✗ Too low", "marketplace.resell.updateResaleOrder": "Update Resale Order", "marketplace.resell.valid": "✓ Valid", "marketplace.resell.originalOwnerRoyalty": "Original owner royalty - {percentage}% ({amount} TON)", "marketplace.resell.youWillReceive": "You will receive: {amount} TON", "marketplace.tabs.activity": "Activity", "marketplace.tabs.buy": "Buy", "marketplace.tabs.sell": "<PERSON>ll", "marketplaceFilters.allCollections": "All Collections", "marketplaceFilters.max": "Max", "marketplaceFilters.min": "Min", "marketplaceFilters.newestFirst": "Newest First", "marketplaceFilters.oldestFirst": "Oldest First", "marketplaceFilters.priceHighToLow": "Price: High to Low", "marketplaceFilters.priceLowToHigh": "Price: Low to High", "marketplaceFilters.sortBy": "Sort by", "mock.message": "Mock message", "notFound.redirecting": "Redirecting...", "notFound.takingYouBackToMarketplace": "Taking you back to the marketplace", "nouns.confirmation": "Confirmation", "nouns.description": "Description", "nouns.email": "Email", "nouns.error": "Error", "nouns.from": "From", "nouns.name": "Name", "nouns.orderNumber": "Order #{number}", "nouns.password": "Password", "nouns.price": "Price", "nouns.service": "Service", "orderActors.anonymousUser": "Anonymous User", "orderActors.buyer": "Buyer", "orderActors.noBuyerAssigned": "No buyer assigned", "orderActors.noRoleAssigned": "No {role} assigned", "orderActors.noSellerAssigned": "No seller assigned", "orderActors.orderActors": "Order Actors", "orderActors.resseller": "<PERSON><PERSON><PERSON>", "orderActors.seller": "<PERSON><PERSON>", "orderDeadlineTimer.deadline": "Deadline", "orderDeadlineTimer.giftWillBecomeTransferable": "Gift will become transferable soon", "orderDeadlineTimer.sellerMustSend": "Seller must send", "orderDeadlineTimer.sendOrLoseCollateral": "Send or lose collateral", "orderDeadlineTimer.waiting": "Waiting", "orderDetails.content.action": "Action", "orderDetails.content.buy": "Buy", "orderDetails.content.fulfill": "Fulfill", "orderDetails.content.insufficientBalance": "Insufficient balance to complete this action", "orderDetails.content.share": "Share", "orderDetails.content.showResellHistory": "Show Resell History", "orderDetails.fees.buyer": "Buyer", "orderDetails.fees.collateral": "Collateral", "orderDetails.fees.collateralDescription": "{buyerPercentage}% collateral for buyers. Locked until the order is fulfilled. Instantly refunded if order is unsuccessful.", "orderDetails.fees.deposited": "Deposited", "orderDetails.fees.feePaidBySeller": "Fee {feePercent}%. Paid by seller.", "orderDetails.fees.gift": "gift", "orderDetails.fees.orderDetailsAndFees": "Order Details & Fees", "orderDetails.fees.purchaseFee": "Purchase Fee", "orderDetails.fees.seller": "<PERSON><PERSON>", "orderDetails.lastUpdate": "Last update", "orderDetailsActionButtons.close": "Close", "orderDetailsActionButtons.processing": "Processing...", "orderDetailsHeaderSection.unknownCollection": "Unknown Collection", "orderDetailsProposalsSection.makeProposal": "Make Proposal", "orderDetailsProposalsSection.priceProposals": "Price Proposals", "orderDetailsUserInfoSection.anonymousUser": "Anonymous User", "orderDetailsUserInfoSection.loading": "Loading...", "orderPageClient.failedToLoadOrder": "Failed to load order", "orderPageClient.orderNotFound": "Order not found", "orderPageClient.redirectingToHome": "Redirecting to home...", "orderStatus.active": "Active", "orderStatus.cancelled": "Cancelled", "orderStatus.created": "Created", "orderStatus.fulfilled": "Fulfilled", "orderStatus.giftSentToRelayer": "Sen<PERSON> to Bot", "orderStatus.paid": "Paid", "orderStatusUtils.active": "Active", "orderStatusUtils.buyerDeadline": "Buyer Deadline", "orderStatusUtils.buyerMustClaimGiftOrLoseCollateral": "Buyer must claim gift or lose collateral", "orderStatusUtils.cancelled": "Cancelled", "orderStatusUtils.claimGiftFromRelayerOrLoseCollateral": "Claim gift from relayer or lose collateral", "orderStatusUtils.created": "Created", "orderStatusUtils.deadline": "Deadline", "orderStatusUtils.fulfilled": "Fulfilled", "orderStatusUtils.giftSentToRelayer": "Sen<PERSON> to Bot", "orderStatusUtils.paid": "Paid", "orderStatusUtils.sellerDeadline": "<PERSON><PERSON>line", "orderStatusUtils.sellerMustSend": "Seller must send", "orderStatusUtils.sellerMustSendGiftOrLoseCollateral": "Seller must send gift or lose collateral", "orderStatusUtils.sendGiftToRelayerOrLoseCollateral": "Send gift to relayer or lose collateral", "orderStatusUtils.sendOrLoseCollateral": "Send or lose collateral", "orderStatusUtils.timeToClaimGift": "Time to <PERSON><PERSON><PERSON>", "orderStatusUtils.timeToSendGift": "Time to Send Gift", "orderTraitsSection.giftTraits": "Gift Traits", "orders.cancelOrder.cancel": "Cancel", "orders.cancelOrder.cancelOrder": "Cancel Order", "orders.cancelOrder.cancellationWarning": "This action cannot be undone.", "orders.cancelOrder.cancelling": "Cancelling...", "orders.cancelOrder.collateralLossDescription": "You will lose {amount} TON in collateral. This action is permanent and cannot be undone.", "orders.cancelOrder.collateralLossWarning": "You will lose {amount} TON in collateral.", "orders.cancelOrder.collateralLost": "Collateral Lost", "orders.cancelOrder.confirmCancellation": "Are you sure you want to cancel this order?", "orders.cancelOrder.failedToCancelOrder": "Failed to cancel order: {message}", "orders.cancelOrder.keepOrder": "Keep Order", "orders.cancelOrder.orderCancelledSuccessfully": "Order cancelled successfully", "orders.cancelOrder.penaltyFeeDescription": "If you cancel this order, a penalty fee of {fee} TON will be taken from your balance. This action is permanent and cannot be undone.", "orders.cancelOrder.resellerEarningsLoss": "Additionally, you will lose {amount} TON in reseller earnings.", "orders.cancelOrder.resellerEarningsWarning": "As a reseller, you will lose your potential earnings.", "orders.cancelOrder.unexpectedError": "An unexpected error occurred", "orders.cancelOrder.warningPenaltyFee": "Warning: Penalty Fee <PERSON>", "orders.clickLoginToSeeOrders": "Click on login Telegram button to see your orders", "orders.noBuyOrdersFound": "No buy orders found", "orders.noSellOrdersFound": "No sell orders found", "orders.tabs.myBuyOrders": "My Buy Orders ({count})", "orders.tabs.mySellOrders": "My Sell Orders ({count})", "orders.userOrderCard.activateOrder": "Activate Order", "orders.userOrderCard.attachGift": "Attach Gift", "orders.userOrderCard.getAGift": "Get a Gift", "orders.userOrderCard.getCancelledGift": "Get Cancelled Gift", "orders.userOrderCard.resellThisOrder": "Resell this order", "orders.userOrderCard.sendAGift": "Send a Gift", "orders.youAreNotLoggedIn": "You are not logged in", "priceProposalDrawer.cancel": "Cancel", "priceProposalDrawer.currentPrice": "Current Price", "priceProposalDrawer.info": "Your proposed price must be lower than the current price. The full amount will be locked as collateral until the proposal is resolved.", "priceProposalDrawer.proposedPrice": "Your Proposed Price", "priceProposalDrawer.savings": "Save {amount} TON ({percentage}%)", "priceProposalDrawer.submitProposal": "Submit Proposal", "priceProposalDrawer.submitting": "Submitting...", "priceProposalDrawer.title": "Propose Price", "profile.form.displayName": "Display Name", "profile.form.editProfile": "Edit Profile", "profile.form.enterYourDisplayName": "Enter your display name", "profile.form.failedToUpdateProfile": "Failed to update profile. Please try again.", "profile.form.nameIsRequired": "Name is required", "profile.form.nameTooLong": "Name must be less than 50 characters", "profile.form.profileUpdatedSuccessfully": "Profile updated successfully!", "profile.form.updateProfile": "Update Profile", "profile.form.updating": "Updating...", "profile.main": "Main", "profile.myTransactions": "My Transactions", "profile.referralSection.anonymous": "Anonymous", "profile.referralSection.failedToLoadReferrals": "Failed to load referrals", "profile.referralSection.failedToShareReferralLink": "Failed to share referral link", "profile.referralSection.friends": "friends", "profile.referralSection.joinMeOnMarketplace": "Join me on this amazing marketplace and start earning rewards!", "profile.referralSection.joinTheMarketplace": "Join the Marketplace", "profile.referralSection.loadingReferralData": "Loading referral data...", "profile.referralSection.name": "Name", "profile.referralSection.ofTheirPurchaseFees": "of their purchase fees", "profile.referralSection.points": "points", "profile.referralSection.potentialEarnings": "Potential Earnings", "profile.referralSection.referralLinkSharedSuccessfully": "Referral link shared successfully!", "profile.referralSection.referralProgram": "Referral Program", "profile.referralSection.referralRateDescription": "You earn {percentage}% of the purchase fee when your referrals make purchases", "profile.referralSection.shareReferralLink": "Share Referral Link", "profile.referralSection.shareTheLinkGetPoints": "Share the link - get points for gifts!", "profile.referralSection.sharing": "Sharing...", "profile.referralSection.yourReferralRate": "Your Referral Rate", "profile.referralSection.yourReferrals": "Your Referrals ({count})", "profile.settings.animatedCollections": "Animated Collections", "profile.settings.animatedCollectionsDescription": "Enable animated collection previews and effects", "profile.settings.settings": "Settings", "profile.socialLinks.followUs": "Follow Us", "profile.socialLinks.followUsOn": "Follow us on {platform}", "profile.transactionHistory.emptyState.noTransactionsYet": "No transactions yet", "profile.transactionHistory.emptyState.transactionHistoryDescription": "Your transaction history will appear here when you start trading on the marketplace", "profile.transactionHistory.header.beta": "BETA", "profile.transactionHistory.header.refresh": "Refresh", "profile.transactionHistory.header.transactionHistory": "Transaction History", "profile.transactionHistory.loadingState.loadingYourTransactions": "Loading your transactions...", "profile.transactionHistory.pagination.loadingMoreTransactions": "Loading more transactions...", "profile.transactionHistory.pagination.reachedEndOfHistory": "You've reached the end of your transaction history", "profile.transactionHistory.table.amount": "Amount", "profile.transactionHistory.table.date": "Date", "profile.transactionHistory.table.description": "Description", "profile.transactionHistory.table.type": "Type", "profile.transactionHistory.transactionHistory": "Transaction History", "profile.userInfo.anonymousUser": "Anonymous User", "profile.userInfo.availableBalance": "Available Balance", "profile.userInfo.lockedBalance": "Locked Balance", "profile.userInfo.myPoints": "My Points", "profile.userInfo.profileInformation": "Profile Information", "profile.userInfo.totalBalance": "Total Balance", "proposalsTable.accept": "Accept", "proposalsTable.accepting": "Accepting...", "proposalsTable.cancel": "Cancel", "proposalsTable.cancelling": "Cancelling...", "proposalsTable.loadMore": "Load More Proposals", "proposalsTable.loadingMore": "Loading...", "proposalsTable.noProposals": "No price proposals yet", "proposalsTable.priceProposals": "Price Proposals", "proposalsTable.proposalAcceptedSuccess": "Price proposal accepted successfully", "proposalsTable.proposalCancelledSuccess": "Price proposal cancelled successfully", "proposalsTable.proposalCreatedSuccess": "Price proposal created successfully", "proposalsTable.statusAccepted": "Accepted", "proposalsTable.statusActive": "Active", "proposalsTable.statusCancelled": "Cancelled", "proposalsTable.yourProposal": "Your Proposal", "purchase.buyer.giftReadyToClaim": "Gift is ready to be claimed from relayer.", "purchase.buyer.paymentCompletedNoFee": "Purchase successful! Payment completed! {netAmountToSeller} TON transferred to seller. {actionMessage}", "purchase.buyer.paymentCompletedWithFee": "Purchase successful! Payment completed! {netAmountToSeller} TON transferred to seller. Purchase fee of {totalFee} TON applied. {actionMessage}", "purchase.buyer.waitingForSeller": "Waiting for seller to send gift.", "purchase.buyer.withLockAndFee": "Purchase successful! {lockedAmount} TON locked ({lockPercentage}% of {orderPrice} TON order). Purchase fee of {totalFee} TON applied. {actionMessage}", "purchase.buyer.withLockNoFee": "Purchase successful! {lockedAmount} TON locked ({lockPercentage}% of {orderPrice} TON order). {actionMessage}", "purchase.feeApplied": "Purchase fee of {totalFee} TON applied.", "purchase.fundsLocked": "{lockedAmount} TON locked ({lockPercentage}% of {orderPrice} TON order).", "purchase.paymentCompleted": "Payment completed! {netAmountToSeller} TON transferred to seller.", "purchase.seller.canSendGift": "You can now send the gift.", "purchase.seller.withLock": "Purchase successful! {lockedAmount} TON locked ({lockPercentage}% of {orderPrice} TON order). {actionMessage}", "purchase.successful": "Purchase successful!", "resellTxHistory.buyer": "Buyer", "resellTxHistory.close": "Close", "resellTxHistory.executionPrice": "Execution Price", "resellTxHistory.failedToFetchHistory": "Failed to fetch resell history", "resellTxHistory.loadingResellHistory": "Loading resell history...", "resellTxHistory.noResellTransactions": "No resell transactions found", "resellTxHistory.resellHistory": "Resell History", "resellTxHistory.resellHistoryCount": "Resell History ({count})", "resellTxHistory.resellTransaction": "Resell Transaction", "resellTxHistory.reseller": "Reseller", "resellTxHistory.showingEarnings": "Showing your earnings from each resell transaction", "resellTxHistory.yourEarnings": "Your Earnings", "secondaryMarketBadge.resell": "Resell", "sellButtonComponent.buy": "Buy", "sellPriceDetails.marketFeesIncluded": "market fees included", "shareLink.checkOutOrder": "Check out this order!", "shareLink.failedToShare": "Failed to share order", "shareLink.orderIdNotAvailable": "Order ID not available", "tonConnect.authenticating": "Authenticating...", "tonConnect.connect": "Connect", "tonConnect.connecting": "Connecting...", "tonConnect.disconnect": "Disconnect", "transaction.description.cancellationCompensationFromBuyerCollateral": "Cancellation compensation from buyer collateral ({amount} TON)", "transaction.description.cancellationPenaltyForBuyer": "Cancellation penalty for buyer ({amount} TON collateral)", "transaction.description.cancellationPenaltyForSeller": "Cancellation penalty for seller ({amount} TON)", "transaction.description.collateralUnlockedDueToAdminCancellation": "Collateral unlocked due to admin cancellation ({amount} TON)", "transaction.description.collateralUnlockedDueToBuyerCancellation": "Collateral unlocked due to buyer cancellation ({amount} TON)", "transaction.description.collateralUnlockedDueToCancellation": "Collateral unlocked due to order cancellation ({amount} TON)", "transaction.description.collateralUnlockedDueToSellerCancellation": "Collateral unlocked due to seller cancellation ({amount} TON)", "transaction.description.depositFromTonWallet": "Deposit from TON wallet (original: {originalAmount} TON, after fees: {netAmount} TON)", "transaction.description.fixedCancellationFeePenalty": "Fixed cancellation fee penalty ({amount} TON)", "transaction.description.lockedCollateralForBuyer": "Locked collateral for buyer ({amount} TON, {percentage}% of {orderPrice} TON order)", "transaction.description.lockedCollateralForSeller": "Locked collateral for seller ({amount} TON, {percentage}% of {orderPrice} TON order)", "transaction.description.proposalCancellationFee": "Price proposal cancellation fee ({amount} TON for order #{orderId})", "transaction.description.proposalCollateralLock": "Locked collateral for price proposal ({amount} TON for order #{orderId})", "transaction.description.proposalCollateralRefund": "Refund from cancelled price proposal ({amount} TON for order #{orderId})", "transaction.description.referralFeeFromPurchase": "Referral fee from purchase ({amount} TON)", "transaction.description.resellFeeEarningsFromBuyerCancellation": "Resell fee earnings from buyer cancellation ({amount} TON)", "transaction.description.saleCompletedForOrder": "Sale completed for order #{orderNumber} ({netAmount} TON net after fees)", "transaction.description.unknown": "Transaction", "transaction.description.unlockedBuyerCollateralForCancelledOrder": "Unlocked buyer collateral for cancelled order #{orderNumber} ({amount} TON)", "transaction.description.withdrawalToTonWallet": "Withdrawal to TON wallet (gross: {grossAmount} TON, net: {netAmount} TON, fee: {feeAmount} TON)", "transaction.type.buyLockCollateral": "Buy Lock", "transaction.type.cancelationFee": "Cancel Fee", "transaction.type.deposit": "<PERSON><PERSON><PERSON><PERSON>", "transaction.type.referralFee": "Referral Fee", "transaction.type.refund": "Refund", "transaction.type.resellFeeEarnings": "Resell Earnings", "transaction.type.sellFulfillment": "Fulfillment", "transaction.type.sellLockCollateral": "Sell Lock", "transaction.type.unlockCollateral": "Unlock", "transaction.type.withdraw": "Withdraw", "unifiedGiftInfoDrawer.activateOrderSteps": "Send the gift directly to the relayer, return to the app, and attach the corresponding gift to your order", "unifiedGiftInfoDrawer.activateYourOrder": "Activate Your Order", "unifiedGiftInfoDrawer.attachGiftToOrder": "Click \"Attach Gift to this Order\" button and select the deposited gift", "unifiedGiftInfoDrawer.claimGiftSteps": "Go to the bot, select 'My Gifts', and select the specific gift you want to withdraw. You will see the corresponding order for that gift. Then go to the primary relayer and write message 'Get a gift'", "unifiedGiftInfoDrawer.claimYourGift": "Claim Your Gift", "unifiedGiftInfoDrawer.close": "Close", "unifiedGiftInfoDrawer.confirmAndGoToRelayer": "Confirm action, and go to {relayerLink} to get your gift", "unifiedGiftInfoDrawer.confirmAndSendToRelayer": "Confirm action, and send this gift to {relayerLink}", "unifiedGiftInfoDrawer.depositGiftToBot": "Deposit your gift to the bot using the \"Deposit a Gift\" button", "unifiedGiftInfoDrawer.getCancelledGift": "Get Cancelled Gift", "unifiedGiftInfoDrawer.getCancelledGiftSteps": "Follow these steps to retrieve your cancelled gift", "unifiedGiftInfoDrawer.goToBot": "Go to {botLink}", "unifiedGiftInfoDrawer.goToRelayerToRetrieve": "Go to \"Primary lawyer\" and write \"Get my gift\"", "unifiedGiftInfoDrawer.instructions": "Instructions:", "unifiedGiftInfoDrawer.openBot": "Open Bot", "unifiedGiftInfoDrawer.pressMuyBuyOrders": "Press on \"My Buy Orders\" button", "unifiedGiftInfoDrawer.pressMySellOrders": "Press on \"My Sell Orders\" button", "unifiedGiftInfoDrawer.pressMySellOrdersCancelled": "Click on \"My Gifts\" button", "unifiedGiftInfoDrawer.pressMySellOrdersPaid": "Press on \"My Sell Orders\" button and select from \"Paid Orders\" group", "unifiedGiftInfoDrawer.pressMySellOrdersWaitingActivation": "Press on \"My Sell Orders\" button and select from \"Waiting for Activation\" group", "unifiedGiftInfoDrawer.selectOrderToActivate": "Select the gift you want to withdraw related to this cancelled order", "unifiedGiftInfoDrawer.selectOrderToGet": "Select your order you want to get", "unifiedGiftInfoDrawer.selectOrderToSend": "Select your order you want to send", "unifiedGiftInfoDrawer.sendGiftSteps": "Send the gift directly to the relayer, then return to the app and attach the gift to your order using the 'Attach Gift' button", "unifiedGiftInfoDrawer.sendGiftToRelayer": "Send Gift to <PERSON><PERSON>", "unifiedGiftInfoDrawer.sendGiftToRelayerToActivate": "Send your gift to {relayer<PERSON>ink} to activate this order", "userOrderActionsSection.cancelOrder": "Cancel Order", "userOrderActionsSection.createResaleOrder": "Create Resale Order", "userOrderActionsSection.showResellHistory": "Show Resell History", "userOrderActionsSection.updateResaleOrder": "Update Resale Order", "userOrderDeadlineSection.giftWillBecomeTransferableSoon": "Gift will become transferable soon", "userOrderDeadlineSection.waiting": "Waiting", "userOrderPricingSection.primaryPrice": "Primary Price", "userOrderPricingSection.secondaryMarketPrice": "Secondary Market Price", "userOrderSellerEarningsSection.earningsDescription": "You will receive your earnings from resell when the order is fulfilled. This amount represents your accumulated earnings from each time this order is resold on the secondary market.", "userOrderSellerEarningsSection.resaleEarnings": "Resale Earnings", "userOrderSellerEarningsSection.totalEarningsFromResales": "Total earnings from resales:", "userOrderStatusAlerts.freezePeriodActive": "Freeze Period Active", "userOrderStatusAlerts.freezePeriodDescription": "Collection items cannot be transferred yet. Wait for the freeze period to end.", "userOrderStatusAlerts.giftReady": "Gift Ready!", "userOrderStatusAlerts.giftReadyDescription": "Your gift has been sent to the relayer. Please visit the bot to claim your gift.", "userOrderStatusAlerts.giftRefundAvailable": "Gift Refund Available", "userOrderStatusAlerts.giftRefundDescription": "Go to the relayer to refund your gift.", "userOrderStatusAlerts.openBotForRefund": "Open Bot for Refund", "userOrderStatusAlerts.openBotToClaim": "Open Bo<PERSON> to <PERSON><PERSON><PERSON>", "userOrderStatusAlerts.readyToSend": "Ready to Send", "userOrderStatusAlerts.readyToSendDescription": "You can now send the gift to the relayer.", "userOrderStatusAlerts.waitingForTransfer": "Waiting for Transfer", "userOrderStatusAlerts.waitingForTransferDescription": "Wait until the collection item becomes transferable.", "userOrderUserInfoSection.noUserAssigned": "No {role} assigned yet", "welcomeModal.choosePreferredLanguage": "Choose your preferred language for the app", "welcomeModal.close": "Close", "welcomeModal.earnRewards": "<PERSON><PERSON><PERSON>", "welcomeModal.failedToShareLink": "Failed to share referral link", "welcomeModal.iSharedIt": "I shared it", "welcomeModal.linkSharedSuccessfully": "Referral link shared successfully", "welcomeModal.selectLanguage": "Select Language", "welcomeModal.shareAppDescription": "Share this app to get up to 5% of revenue from each other user trades and get the points to buy gifts for free", "welcomeModal.shareReferralLink": "Share referral link", "welcomeModal.shareText": "Join me on the marketplace and start trading gifts!", "welcomeModal.shareTitle": "Join the Marketplace", "welcomeModal.sharing": "Sharing...", "welcomeModal.skip": "<PERSON><PERSON>", "withdrawDrawer.amountMustBeAtLeast": "Amount must be at least 1 TON", "withdrawDrawer.availableBalance": "Available balance:", "withdrawDrawer.cancel": "Cancel", "withdrawDrawer.enterAmountToWithdraw": "Enter amount to withdraw", "withdrawDrawer.insufficientAvailableBalance": "Insufficient available balance", "withdrawDrawer.insufficientBalance": "Insufficient available balance", "withdrawDrawer.invalidAmount": "Invalid amount", "withdrawDrawer.invalidWithdrawalAmount": "Invalid withdrawal amount", "withdrawDrawer.limitResetsAt": "Limit resets at:", "withdrawDrawer.loadingConfiguration": "Loading configuration...", "withdrawDrawer.max": "Max", "withdrawDrawer.minTonPlaceholder": "Min 1 TON", "withdrawDrawer.minimumWithdrawal": "Minimum withdrawal:", "withdrawDrawer.minimumWithdrawalAmount": "Minimum withdrawal amount is 1 TON", "withdrawDrawer.netAmount": "Net amount:", "withdrawDrawer.noWalletAddressFound": "No wallet address found in your profile", "withdrawDrawer.pleaseConnectWallet": "Please connect your wallet to make a withdrawal", "withdrawDrawer.pleaseConnectWalletFirst": "Please connect your wallet first", "withdrawDrawer.processing": "Processing...", "withdrawDrawer.remainingLimit": "Remaining limit:", "withdrawDrawer.unexpectedError": "An unexpected error occurred", "withdrawDrawer.withdraw": "Withdraw", "withdrawDrawer.withdrawAmount": "Withdraw amount:", "withdrawDrawer.withdrawAmountTon": "With<PERSON><PERSON> Amount (TON)", "withdrawDrawer.withdrawFunds": "Withdraw Funds", "withdrawDrawer.withdrawTonToWallet": "Withdraw TON to your connected wallet", "withdrawDrawer.withdrawalFailed": "<PERSON><PERSON><PERSON> failed: {message}", "withdrawDrawer.withdrawalFee": "Withdrawal fee:", "withdrawDrawer.withdrawalInformation": "Withdrawal Information", "withdrawDrawer.withdrawalLimit24h": "24-hour withdrawal limit:", "withdrawDrawer.withdrawalSuccessful": "Withdrawal successful! Transaction: {hash}", "withdrawDrawer.youWillReceive": "You will receive:", "withdrawGiftDrawer.close": "Close", "withdrawGiftDrawer.instructionStep1": "Go to {botLink}, click on \"My Gifts\", and select the gift you want to withdraw", "withdrawGiftDrawer.instructionStep2": "Then, go to {relayerLink} and write a message \"Get a gift\"", "withdrawGiftDrawer.instructionStep3": "Go to a relayer and write \"Get my gift.\"", "withdrawGiftDrawer.openBot": "Open Bot", "withdrawGiftDrawer.withdrawGift": "Withdraw Gift", "withdrawGiftDrawer.withdrawInstructions": "1. Go to the bot, click on 'My Gifts', and select the gift you want to withdraw 2. Then, go to the primary relayer and write a message 'Get a gift'"}