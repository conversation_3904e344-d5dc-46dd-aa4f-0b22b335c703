'use client';

import { Avatar } from '@telegram-apps/telegram-ui';
import { ChevronDown, Loader2, User } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useIntl } from 'react-intl';

import { getUserById } from '@/api/auth-api';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { ADMIN_DEFAULT_NAME } from '@/core.constants';
import { cn } from '@/lib/utils';
import type { UserEntity } from '@/mikerudenko/marketplace-shared';
import { globalCache } from '@/utils/cache-utils';

import { orderActorsMessages } from './order-actors/intl/order-actors.messages';

// Cache configuration for user data
const USER_CACHE_CONFIG = { duration: 10 * 60 * 1000 }; // 10 minutes

// Helper function to generate cache key for user data
function getUserCacheKey(userId: string): string {
  return `order-actor-user:${userId}`;
}

interface OrderActorsProps {
  buyerId?: string;
  sellerId?: string;
  isResellOrder?: boolean;
}

interface UserRowSectionProps {
  userId: string;
  role: 'Buyer' | 'Seller' | 'Resseller';
}

function UserRowSection({ userId, role }: UserRowSectionProps) {
  const { formatMessage: t } = useIntl();
  const [userInfo, setUserInfo] = useState<UserEntity | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const loadUserInfo = async () => {
      if (!userId) {
        setUserInfo(null);
        setLoading(false);
        return;
      }

      const cacheKey = getUserCacheKey(userId);

      // Check cache first
      const cachedUser = globalCache.get<UserEntity>(cacheKey);
      if (cachedUser) {
        setUserInfo(cachedUser);
        setLoading(false);
        return;
      }

      setLoading(true);
      try {
        const user = await getUserById(userId);
        setUserInfo(user);

        // Cache the user data if it exists
        if (user) {
          globalCache.set(cacheKey, user, USER_CACHE_CONFIG);
        }
      } catch (error) {
        console.error(`Error loading ${role.toLowerCase()} info:`, error);
        setUserInfo(null);
      } finally {
        setLoading(false);
      }
    };

    loadUserInfo();
  }, [userId, role]);

  return (
    <div className="backdrop-blur-sm rounded-xl border border-[#3a4a5c]/30 shadow-md hover:shadow-lg transition-all duration-200 hover:bg-[#232e3c]/50 p-4">
      {loading ? (
        <div className="flex flex-col items-center justify-center gap-2">
          <Loader2 className="w-4 h-4 animate-spin text-[#6ab2f2]" />
          <span className="text-[#708499] text-xs">Loading...</span>
        </div>
      ) : userInfo ? (
        <div className="flex flex-col items-center text-center space-y-2">
          <div className="px-2 py-1 bg-[#6ab2f2]/10 rounded-full border border-[#6ab2f2]/20">
            <p className="text-[#6ab2f2] text-xs font-semibold uppercase tracking-wider">
              {role}
            </p>
          </div>

          <div className="relative">
            {userInfo.photoURL ? (
              <Avatar
                size={28}
                src={userInfo.photoURL}
                className="ring-1 ring-[#6ab2f2]/30 shadow-sm"
              />
            ) : (
              <div className="w-7 h-7 bg-gradient-to-br from-[#3a4a5c] to-[#2a3441] rounded-full flex items-center justify-center ring-1 ring-[#6ab2f2]/30 shadow-sm">
                <User className="w-3.5 h-3.5 text-[#708499]" />
              </div>
            )}
          </div>

          <div className="space-y-1">
            <p className="text-[#f5f5f5] font-medium text-sm leading-tight">
              {userInfo.role === 'admin'
                ? ADMIN_DEFAULT_NAME
                : (userInfo.displayName ??
                  userInfo.name ??
                  t(orderActorsMessages.anonymousUser))}
            </p>
          </div>
        </div>
      ) : (
        <div className="flex flex-col items-center text-center space-y-2">
          <div className="px-2 py-1 bg-[#6ab2f2]/10 rounded-full border border-[#6ab2f2]/20">
            <p className="text-[#6ab2f2] text-xs font-semibold uppercase tracking-wider">
              {role}
            </p>
          </div>
          <div className="w-7 h-7 bg-gradient-to-br from-[#3a4a5c] to-[#2a3441] rounded-full flex items-center justify-center ring-1 ring-[#6ab2f2]/30 shadow-sm">
            <User className="w-3.5 h-3.5 text-[#708499]" />
          </div>
          <p className="text-[#708499] text-xs">
            {t(orderActorsMessages.noRoleAssigned, {
              role: role.toLowerCase(),
            })}
          </p>
        </div>
      )}
    </div>
  );
}

export function OrderActors({
  buyerId,
  sellerId,
  isResellOrder,
}: OrderActorsProps) {
  const { formatMessage: t } = useIntl();
  const [isOpen, setIsOpen] = useState(false);

  const hasBuyer = Boolean(buyerId);
  const hasSeller = Boolean(sellerId);

  if (!hasBuyer && !hasSeller) {
    return null;
  }

  const hasOnlyOne = (hasBuyer && !hasSeller) || (!hasBuyer && hasSeller);
  const hasBoth = hasBuyer && hasSeller;

  return (
    <div className="space-y-4">
      <Collapsible defaultOpen={false} open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger className="flex items-center justify-between w-full p-4 bg-[#1e2337]/50 rounded-lg hover:bg-[#1e2337]/70 transition-colors duration-200">
          <span className="text-[#f5f5f5] font-medium">
            {t(orderActorsMessages.orderActors)}
          </span>
          <ChevronDown
            className={cn(
              'h-4 w-4 text-[#708499] transition-transform duration-300 ease-in-out',
              isOpen && 'rotate-180',
            )}
          />
        </CollapsibleTrigger>

        <CollapsibleContent className="overflow-hidden transition-all duration-300 ease-in-out data-[state=closed]:animate-collapsible-up data-[state=open]:animate-collapsible-down">
          <div className="px-4 pb-4 bg-[#1e2337]/50 rounded-lg">
            <div
              className={cn(
                'grid gap-3 pt-3',
                hasOnlyOne &&
                  'grid-cols-1 justify-center max-w-[200px] mx-auto',
                hasBoth && 'grid-cols-2',
              )}
            >
              {hasBuyer && (
                <UserRowSection
                  userId={buyerId!}
                  role={isResellOrder ? 'Resseller' : 'Buyer'}
                />
              )}
              {hasSeller && (
                <UserRowSection userId={sellerId!} role={'Seller'} />
              )}
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
}
