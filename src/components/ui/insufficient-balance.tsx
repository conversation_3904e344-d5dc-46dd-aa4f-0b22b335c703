'use client';

import { Caption } from '@telegram-apps/telegram-ui';
import { Plus } from 'lucide-react';
import { useIntl } from 'react-intl';

import { Button } from '@/components/ui/button';

interface InsufficientBalanceMessageProps {
  message: string;
  onTopUp: () => void;
  className?: string;
}

export function InsufficientBalance({
  message,
  onTopUp,
  className = '',
}: InsufficientBalanceMessageProps) {
  const { formatMessage: t } = useIntl();

  return (
    <div className={`text-center space-y-2 ${className}`}>
      <Caption level="2" weight="3" className="text-[#ec3942]">
        {message}
      </Caption>
      <Button
        variant="outline"
        onClick={onTopUp}
        className="w-full h-12 mt-3 rounded-2xl text-base font-medium flex items-center"
      >
        <span>
          {t({
            id: 'insufficientBalance.topUp',
            defaultMessage: 'Top up balance',
          })}
        </span>
        <Plus className="w-5 h-5 -ml-1.25 translate-y-[1px]" />
      </Button>
    </div>
  );
}
