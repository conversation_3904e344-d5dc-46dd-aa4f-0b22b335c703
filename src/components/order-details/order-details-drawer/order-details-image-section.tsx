import { TgsOrImage } from '@/components/tgs/tgs-or-image';
import { TgsOrImageGift } from '@/components/tgs/tgs-or-image-gift';
import { TgsSkeleton } from '@/components/tgs/tgs-skeleton';
import { cn } from '@/lib/utils';
import type {
  CollectionEntity,
  OrderGift,
} from '@/mikerudenko/marketplace-shared';

interface OrderDetailsImageSectionProps {
  collectionId: string;
  collection: CollectionEntity | null;
  gift?: OrderGift | null;
  loading?: boolean;
  hasGiftIdList?: boolean;
  bottom?: React.ReactNode;
}

export function OrderDetailsImageSection({
  collectionId,
  collection,
  gift,
  loading = false,
  hasGiftIdList = false,
  bottom,
}: OrderDetailsImageSectionProps) {
  // Only show skeleton if we're actively loading AND don't have any data yet
  // If we have either gift data OR we know there's no gift (hasGiftIdList=false), show content immediately
  const shouldShowSkeleton = loading && hasGiftIdList && !gift;

  return (
    <div className="relative">
      <div
        className={cn(
          'w-full mx-auto aspect-square relative rounded-2xl overflow-hidden bg-gradient-to-br from-[#232e3c] to-[#1a252f] border border-[#3a4a5c]/50 p-8',
          gift && 'p-0',
        )}
      >
        {shouldShowSkeleton ? (
          <TgsSkeleton
            className="w-full h-full"
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
            }}
          />
        ) : gift ? (
          <TgsOrImageGift
            isImage={false}
            gift={gift}
            className="w-full h-full"
            style={{ width: '100%', height: '100%' }}
          />
        ) : (
          <TgsOrImage
            isImage={false}
            collectionId={collectionId}
            imageProps={{
              alt: collection?.name || 'Order item',
              fill: true,
              className: 'object-contain drop-shadow-2xl',
            }}
            tgsProps={{
              style: { height: '100%', width: '100%' },
            }}
          />
        )}
        {bottom && (
          <div className="w-full flex items-center justify-between absolute bottom-0 left-0 px-2 pb-2 z-60">
            {bottom}
          </div>
        )}
      </div>
    </div>
  );
}
