'use client';

import { ChevronDown } from 'lucide-react';
import { useState } from 'react';
import { useIntl } from 'react-intl';

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { cn } from '@/lib/utils';
import type { OrderEntity, UserType } from '@/mikerudenko/marketplace-shared';

import { orderDetailsProposalsSectionMessages } from './intl/order-details-proposals-section.messages';
import { ProposalsTable } from './proposals';

interface OrderDetailsProposalsSectionProps {
  order: OrderEntity;
  userType?: UserType;
  onAcceptProposal?: (proposalId: string) => void;
  onCancelProposal?: (proposalId: string) => void;
  acceptingProposalId?: string;
  cancellingProposal?: boolean;
  proposalsRefreshKey?: number;
}

export function OrderDetailsProposalsSection({
  order,
  userType,
  onAcceptProposal,
  onCancelProposal,
  acceptingProposalId,
  cancellingProposal,
  proposalsRefreshKey,
}: OrderDetailsProposalsSectionProps) {
  const { formatMessage: t } = useIntl();
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="space-y-4">
      <Collapsible defaultOpen={false} open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger className="flex items-center justify-between w-full p-4 bg-[#1e2337]/50 rounded-lg hover:bg-[#1e2337]/70 transition-colors duration-200">
          <div className="flex items-center gap-3">
            <span className="text-[#f5f5f5] font-medium">
              {t(orderDetailsProposalsSectionMessages.priceProposals)}
            </span>
          </div>
          <ChevronDown
            className={cn(
              'h-4 w-4 text-[#708499] transition-transform duration-300 ease-in-out',
              isOpen && 'rotate-180',
            )}
          />
        </CollapsibleTrigger>

        <CollapsibleContent className="overflow-hidden transition-all duration-300 ease-in-out data-[state=closed]:animate-collapsible-up data-[state=open]:animate-collapsible-down">
          <div className="px-4 pb-4 space-y-4 bg-[#1e2337]/50 rounded-lg">
            <ProposalsTable
              key={proposalsRefreshKey}
              order={order}
              userType={userType}
              onAcceptProposal={onAcceptProposal}
              onCancelProposal={onCancelProposal}
              acceptingProposalId={acceptingProposalId}
              cancellingProposal={cancellingProposal}
            />
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
}
