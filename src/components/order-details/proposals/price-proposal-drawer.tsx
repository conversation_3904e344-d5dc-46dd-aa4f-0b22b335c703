'use client';

import { Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import { Drawer } from 'vaul';

import {
  canUserProposeOnOrder,
  getMaximumProposalPrice,
  proposeOrderPrice,
  validateProposedPrice,
} from '@/api/proposal-api';
import { formatServerError } from '@/api/server-error-handler';
import { TonLogo } from '@/components/TonLogo';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { NumberInput } from '@/components/ui/number-input';
import { useToast } from '@/hooks/use-toast';
import { useVisualViewport } from '@/hooks/use-visual-viewport';
import { roundToThreeDecimals } from '@/lib/utils';
import type { OrderEntity } from '@/mikerudenko/marketplace-shared';
import { useRootContext } from '@/root-context';

import { priceProposalDrawerMessages } from './intl/price-proposal-drawer.messages';
import { proposalsTableMessages } from './intl/proposals-table.messages';

interface PriceProposalDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: OrderEntity | null;
  onProposalCreated?: () => void;
}

export function PriceProposalDrawer({
  open,
  onOpenChange,
  order,
  onProposalCreated,
}: PriceProposalDrawerProps) {
  const { formatMessage: t } = useIntl();
  const { currentUser } = useRootContext();
  const { toast } = useToast();

  const [proposedPrice, setProposedPrice] = useState<number | undefined>();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [maximumProposalPrice, setMaximumProposalPrice] = useState<
    number | null
  >(null);
  const [loadingMaxPrice, setLoadingMaxPrice] = useState(false);
  const drawerContentRef = useVisualViewport({ enabled: open, offset: 64 });

  // Load maximum proposal price when drawer opens
  useEffect(() => {
    if (open && order?.id) {
      const loadMaximumPrice = async () => {
        setLoadingMaxPrice(true);
        try {
          const maxPrice = await getMaximumProposalPrice(order.id!);
          setMaximumProposalPrice(maxPrice);
        } catch (error) {
          console.error('Error loading maximum proposal price:', error);
        } finally {
          setLoadingMaxPrice(false);
        }
      };
      loadMaximumPrice();
    } else if (!open) {
      // Reset form when drawer is closed
      resetForm();
    }
  }, [open, order?.id]);

  const handleClose = () => {
    setProposedPrice(undefined);
    setError(null);
    setMaximumProposalPrice(null);
    onOpenChange(false);
  };

  const resetForm = () => {
    setProposedPrice(undefined);
    setError(null);
    setMaximumProposalPrice(null);
  };

  const handleSubmit = async () => {
    if (!order?.id || !currentUser?.id || !proposedPrice) return;

    // Validate proposed price
    const priceValidation = validateProposedPrice(
      proposedPrice,
      order.price,
      maximumProposalPrice,
    );
    if (!priceValidation.isValid) {
      setError(priceValidation.error || 'Invalid price');
      return;
    }

    // Validate user can propose
    const userValidation = canUserProposeOnOrder(order, currentUser);
    if (!userValidation.canPropose) {
      setError(userValidation.reason || 'Cannot propose on this order');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const result = await proposeOrderPrice(order.id!, proposedPrice);

      if (result.success) {
        // Show success toast
        toast({
          title: t(proposalsTableMessages.proposalCreatedSuccess),
        });

        // Reset form and close only the proposal drawer
        resetForm();
        onOpenChange(false);

        if (onProposalCreated) {
          onProposalCreated();
        }
      } else {
        setError(result.message);
      }
    } catch (error) {
      console.error('Error creating proposal:', error);
      setError(formatServerError(error, t));
    } finally {
      setLoading(false);
    }
  };

  const isValidPrice = () => {
    if (proposedPrice === undefined || proposedPrice <= 0 || !order) {
      return false;
    }

    // Must be less than order price
    if (proposedPrice >= order.price) {
      return false;
    }

    // Must be greater than maximum existing proposal price (if any)
    if (
      maximumProposalPrice !== null &&
      proposedPrice <= maximumProposalPrice
    ) {
      return false;
    }

    return true;
  };

  const getSavingsAmount = () => {
    if (!order || !proposedPrice) return 0;
    return order.price - proposedPrice;
  };

  const getSavingsPercentage = () => {
    if (!order || !proposedPrice) return 0;
    const savings = getSavingsAmount();
    return (savings / order.price) * 100;
  };

  const getMaxAllowedPrice = () => {
    if (order?.price) {
      return order.price - 0.001;
    }
    return undefined;
  };

  const getMinAllowedPrice = () => {
    if (maximumProposalPrice !== null) {
      return maximumProposalPrice + 0.001;
    }
    return 0.001;
  };

  if (!order) return null;

  return (
    <Drawer.Root open={open} onOpenChange={onOpenChange}>
      <Drawer.Portal>
        <Drawer.Overlay className="fixed inset-0 bg-black/40 z-[100]" />
        <Drawer.Content
          ref={drawerContentRef}
          className="bg-[#17212b] flex flex-col rounded-t-[20px] mt-16 fixed bottom-0 left-0 right-0 z-[101] outline-none focus:outline-none max-h-[90vh]"
        >
          <Drawer.Title />
          <div className="p-6 bg-[#17212b] rounded-t-[20px] flex-1 overflow-y-auto">
            <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-[#708499] mb-6 cursor-grab active:cursor-grabbing touch-manipulation" />

            <div className="max-w-md mx-auto space-y-6">
              <div className="text-center mb-6">
                <h2 className="text-xl font-semibold text-[#f5f5f5]">
                  {t(priceProposalDrawerMessages.title)}
                </h2>
              </div>
              <div className="bg-[#1a2332] rounded-lg p-4 border border-[#3a4a5c]">
                <div className="text-sm text-gray-400 mb-1">
                  {t(priceProposalDrawerMessages.currentPrice)}
                </div>
                <div className="flex items-center gap-1">
                  <span className="text-xl font-bold text-white">
                    {roundToThreeDecimals(order.price)}
                  </span>
                  <TonLogo size={20} />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="proposedPrice" className="text-white">
                  {t(priceProposalDrawerMessages.proposedPrice)}
                </Label>
                <div className="relative">
                  <NumberInput
                    id="proposedPrice"
                    placeholder="0.000"
                    value={proposedPrice}
                    onValueChange={setProposedPrice}
                    className="bg-[#1a2332] border-[#3a4a5c] text-white pr-12"
                    disabled={loading || loadingMaxPrice}
                    min={getMinAllowedPrice()}
                    max={getMaxAllowedPrice()}
                    decimalScale={3}
                    fixedDecimalScale={false}
                  />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <TonLogo size={16} />
                  </div>
                </div>

                {proposedPrice && isValidPrice() && (
                  <div className="text-sm text-green-400">
                    {t(priceProposalDrawerMessages.savings, {
                      amount: roundToThreeDecimals(getSavingsAmount()),
                      percentage: getSavingsPercentage().toFixed(1),
                    })}
                  </div>
                )}

                {maximumProposalPrice !== null && (
                  <div className="text-sm text-yellow-400">
                    Must be higher than existing proposals (max:{' '}
                    {roundToThreeDecimals(maximumProposalPrice)} TON)
                  </div>
                )}
              </div>

              <div className="bg-[#232e3c] rounded-lg p-4 border border-[#6ab2f2]/20">
                <div className="text-sm text-[#6ab2f2]">
                  {t(priceProposalDrawerMessages.info)}
                </div>
              </div>

              {error && (
                <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
                  <div className="text-sm text-red-400">{error}</div>
                </div>
              )}

              <div className="space-y-3">
                <Button
                  onClick={handleSubmit}
                  disabled={!isValidPrice() || loading}
                  className="w-full h-12 bg-[#6ab2f2] hover:bg-[#6ab2f2]/90 text-white rounded-2xl"
                >
                  {loading ? (
                    <>
                      <Loader2 className="w-5 h-5 animate-spin mr-2" />
                      {t(priceProposalDrawerMessages.submitting)}
                    </>
                  ) : (
                    t(priceProposalDrawerMessages.submitProposal)
                  )}
                </Button>

                <Button
                  onClick={handleClose}
                  disabled={loading}
                  variant="outline"
                  className="w-full h-12 border-[#3a4a5c] text-[#f5f5f5] hover:bg-[#232e3c]/50 bg-transparent rounded-2xl"
                >
                  {t(priceProposalDrawerMessages.cancel)}
                </Button>
              </div>
            </div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
}
