import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import type {
  CollectionEntity,
  OrderEntity,
} from '@/mikerudenko/marketplace-shared';
import { getOrderDisplayNumber } from '@/services/order-service';

interface UserOrderDetailsSectionProps {
  collection: CollectionEntity | null;
  order: OrderEntity;
  className?: string;
}

export function UserOrderDetailsSection({
  collection,
  order,
  className,
}: UserOrderDetailsSectionProps) {
  return (
    <div className={cn('text-center space-y-2', className)}>
      <div className="flex items-center justify-center gap-3">
        <div className="text-xl font-bold text-[#f5f5f5]">
          {collection?.name || 'Unknown Collection'}
        </div>
        <Badge
          variant="secondary"
          className="bg-[#6ab2f2]/10 text-[#6ab2f2] border-[#6ab2f2]/20 font-semibold text-sm px-2 py-1"
        >
          {getOrderDisplayNumber(order)}
        </Badge>
      </div>
    </div>
  );
}
