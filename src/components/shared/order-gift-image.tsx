'use client';

import { useLocalStorage } from 'usehooks-ts';

import { TgsOrImage } from '@/components/tgs/tgs-or-image';
import { TgsOrImageGift } from '@/components/tgs/tgs-or-image-gift';
import { LocalStorageKeys } from '@/core.constants';
import { useOrderGift } from '@/hooks/use-order-gift';
import { cn } from '@/lib/utils';
import type {
  CollectionEntity,
  OrderEntity,
} from '@/mikerudenko/marketplace-shared';

import { TgsSkeleton } from '../ui';

interface OrderGiftImageProps {
  order: OrderEntity;
  collection: CollectionEntity | null | undefined;
  className?: string;
  children?: React.ReactNode;
  badge?: React.ReactNode;
  // Optional override for animation preference
  forceImage?: boolean;
  // Optional style override for gift component
  giftStyle?: React.CSSProperties;
}

export function OrderGiftImage({
  order,
  collection,
  className,
  children,
  badge,
  forceImage,
  giftStyle,
}: OrderGiftImageProps) {
  const [isAnimatedCollection] = useLocalStorage(
    LocalStorageKeys.IS_ANIMATED_COLLECTION,
    false,
  );

  const { gift, loading } = useOrderGift(order);
  const isImage = forceImage ?? !isAnimatedCollection;

  // Only show loading spinner if we're actively loading AND expect a gift but don't have it yet
  const shouldShowLoading = loading && order.gift_id_list && !gift;

  return (
    <div
      className={cn(
        'aspect-square relative rounded-lg overflow-hidden bg-[#17212b]',
        className,
      )}
    >
      <div className="absolute top-0 left-0 z-50">{badge}</div>

      {gift ? (
        <TgsOrImageGift
          isImage={isImage}
          gift={gift}
          className="w-full h-full"
          style={giftStyle || { width: '100%', height: '100%' }}
        />
      ) : shouldShowLoading ? (
        <TgsSkeleton
          className="w-full h-full"
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
          }}
        />
      ) : (
        <TgsOrImage
          isImage={isImage}
          collectionId={order.collectionId}
          imageProps={{
            alt: collection?.name ?? 'Order item',
            fill: true,
            className: cn(
              'object-cover group-hover:scale-105 transition-transform duration-200 p-4',
            ),
          }}
          tgsProps={{
            style: { height: 'auto', width: 'auto', padding: '16px' },
          }}
        />
      )}

      {children}
    </div>
  );
}
