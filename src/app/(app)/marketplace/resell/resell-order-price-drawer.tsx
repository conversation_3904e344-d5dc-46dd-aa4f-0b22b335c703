'use client';

import { AlertTriangle, TrendingUp } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import { toast } from 'sonner';

import { getAppConfig } from '@/api/app-config-api';
import { setSecondaryMarketPrice } from '@/api/order-api';
import { formatServerError } from '@/api/server-error-handler';
import { PriceLabel } from '@/components/shared/price-label';
import { BaseDrawer } from '@/components/ui/drawer/base-drawer';
import { DrawerActions } from '@/components/ui/drawer/drawer-actions';
import { DrawerHeader } from '@/components/ui/drawer/drawer-header';
import { Input } from '@/components/ui/input';
import { bpsToDecimal, safeMultiply } from '@/lib/utils';
import type { OrderEntity } from '@/mikerudenko/marketplace-shared';
import { calculateResellRoyalty } from '@/services/order-service';

import { resellOrderPriceDrawerMessages } from './intl/resell-order-price-drawer.messages';

interface ResellOrderPriceDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: OrderEntity | null;
  onOrderResold: () => void;
}

export function ResellOrderPriceDrawer({
  open,
  onOpenChange,
  order,
  onOrderResold,
}: ResellOrderPriceDrawerProps) {
  const { formatMessage: t } = useIntl();
  const [price, setPrice] = useState('');
  const [minPrice, setMinPrice] = useState(1);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const loadMinPrice = async () => {
      try {
        const config = await getAppConfig();
        if (config?.min_secondary_market_price) {
          setMinPrice(config.min_secondary_market_price);
        }
      } catch (error) {
        console.error('Error loading min price:', error);
      }
    };

    if (open) {
      loadMinPrice();
      setPrice('');
    }
  }, [open]);

  const priceValue = parseFloat(price);
  const buyerLockPercentageBPS = order?.fees?.buyer_locked_percentage ?? 0;
  const sellerLockPercentageBPS = order?.fees?.seller_locked_percentage ?? 0;
  const buyerLockPercentage = bpsToDecimal(buyerLockPercentageBPS);
  const sellerLockPercentage = bpsToDecimal(sellerLockPercentageBPS);

  const buyerLockedAmount = safeMultiply(
    order?.price ?? 0,
    buyerLockPercentage,
  );
  const sellerLockedAmount = safeMultiply(
    order?.price ?? 0,
    sellerLockPercentage,
  );
  const totalCollateral = buyerLockedAmount + sellerLockedAmount;

  const isValidPrice =
    !isNaN(priceValue) &&
    priceValue >= minPrice &&
    priceValue <= totalCollateral;
  const isPriceTooHigh = !isNaN(priceValue) && priceValue > totalCollateral;

  const handleSetPrice = async () => {
    if (!order?.id || !isValidPrice) return;

    setLoading(true);
    try {
      const result = await setSecondaryMarketPrice(order.id, priceValue);

      if (result.success) {
        toast.success(t(resellOrderPriceDrawerMessages.successMessage));
        onOrderResold();
      } else {
        toast.error(
          result.message ||
            t(resellOrderPriceDrawerMessages.failedToCreateOrder),
        );
      }
    } catch (error) {
      console.error('Error setting secondary market price:', error);
      const errorMessage = formatServerError(error, t);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  if (!order) return null;

  return (
    <BaseDrawer
      open={open}
      onOpenChange={onOpenChange}
      zIndex={100}
      className="mt-32"
      enableVisualViewport={true}
      visualViewportOffset={64}
    >
      <DrawerHeader
        icon={TrendingUp}
        title={t(resellOrderPriceDrawerMessages.setResalePrice)}
        subtitle={t(resellOrderPriceDrawerMessages.setResalePriceSubtitle)}
      />

      <div className="space-y-6">
        <div className="bg-[#232e3c] rounded-xl p-4 border border-[#3a4a5c]">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-[#708499]">
              {t(resellOrderPriceDrawerMessages.originalPrice)}
            </span>
            <PriceLabel
              amount={order.price}
              size={24}
              className="text-lg font-bold text-[#f5f5f5]"
              clickable={false}
            />
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-[#708499]">
              Order #{order.number || order.id?.slice(-6)}
            </span>
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <label
              htmlFor="resale-price-input"
              className="block text-[#f5f5f5] font-medium mb-2"
            >
              {t(resellOrderPriceDrawerMessages.resalePriceTon)}
            </label>
            <Input
              id="resale-price-input"
              type="number"
              step="0.01"
              min={minPrice}
              placeholder={t(
                resellOrderPriceDrawerMessages.minimumTonPlaceholder,
                { minPrice },
              )}
              value={price}
              onChange={(e) => setPrice(e.target.value)}
              className="bg-[#232e3c] border-[#3a4a5c] text-[#f5f5f5] placeholder-[#708499] rounded-xl h-12"
            />
            <div className="flex justify-between items-center mt-2 text-sm">
              <span className="text-[#708499]">
                {t(resellOrderPriceDrawerMessages.minimumPrice, { minPrice })}
              </span>
              {priceValue > 0 && (
                <span
                  className={`font-medium ${isValidPrice ? 'text-green-400' : 'text-red-400'}`}
                >
                  {isValidPrice
                    ? t(resellOrderPriceDrawerMessages.valid)
                    : isPriceTooHigh
                      ? t(resellOrderPriceDrawerMessages.tooHigh)
                      : t(resellOrderPriceDrawerMessages.tooLow)}
                </span>
              )}
            </div>
            {totalCollateral > 0 && (
              <>
                <div className="mt-1 text-sm text-[#708499]">
                  {t(resellOrderPriceDrawerMessages.maximumCollateral, {
                    amount: totalCollateral.toFixed(2),
                  })}
                </div>
                <div className="mt-1 text-sm text-[#708499]">
                  {(() => {
                    const royaltyCalc = calculateResellRoyalty(order, 0);
                    return t(
                      resellOrderPriceDrawerMessages.originalOwnerRoyalty,
                      {
                        percentage: royaltyCalc.royaltyPercentage.toFixed(1),
                        amount: royaltyCalc.royaltyAmount.toFixed(2),
                      },
                    );
                  })()}
                  {price &&
                    (() => {
                      const royaltyCalc = calculateResellRoyalty(
                        order,
                        Number(price),
                      );
                      return (
                        <span>
                          {' '}
                          {t(resellOrderPriceDrawerMessages.youWillReceive, {
                            amount: royaltyCalc.sellerEarnings.toFixed(2),
                          })}
                        </span>
                      );
                    })()}
                </div>
              </>
            )}
          </div>

          {isPriceTooHigh && (
            <div className="bg-red-900/20 rounded-xl p-4 border border-red-500/30">
              <div className="flex items-start gap-3">
                <AlertTriangle className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
                <div className="space-y-1">
                  <p className="text-sm font-medium text-red-400">
                    {t(resellOrderPriceDrawerMessages.priceTooHighTitle)}
                  </p>
                  <p className="text-xs text-red-300 leading-relaxed">
                    {t(resellOrderPriceDrawerMessages.priceTooHighDescription, {
                      amount: totalCollateral.toFixed(2),
                    })}
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="bg-[#2a3441] rounded-xl p-4 border border-[#3a4a5c]">
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-5 h-5 text-yellow-500 flex-shrink-0 mt-0.5" />
              <div className="space-y-1">
                <p className="text-sm font-medium text-[#f5f5f5]">
                  {t(resellOrderPriceDrawerMessages.importantNotice)}
                </p>
                <p className="text-xs text-[#708499] leading-relaxed">
                  {t(resellOrderPriceDrawerMessages.importantNoticeDescription)}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <DrawerActions
        onPrimary={handleSetPrice}
        onCancel={handleCancel}
        primaryLabel={t(resellOrderPriceDrawerMessages.setResalePriceButton)}
        primaryLoadingLabel={t(resellOrderPriceDrawerMessages.settingPrice)}
        cancelLabel={t(resellOrderPriceDrawerMessages.cancel)}
        primaryDisabled={!isValidPrice}
        loading={loading}
        withConfirm
      />
    </BaseDrawer>
  );
}
