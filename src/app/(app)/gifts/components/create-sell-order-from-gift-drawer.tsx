'use client';

import { useState } from 'react';
import { useIntl } from 'react-intl';
import { toast } from 'sonner';

import { createSellOrderFromGift } from '@/api/gifts.api';
import { Button } from '@/components/ui/button';
import { BaseDrawer } from '@/components/ui/drawer/base-drawer';
import { DrawerHeader } from '@/components/ui/drawer/drawer-header';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import type { GiftEntity } from '@/mikerudenko/marketplace-shared';

import { createSellOrderDrawerMessages } from './intl/create-sell-order-drawer.messages';

interface CreateSellOrderDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  gift: GiftEntity | null;
  onOrderCreated: () => void;
}

export function CreateSellOrderFromGiftDrawer({
  open,
  onOpenChange,
  gift,
  onOrderCreated,
}: CreateSellOrderDrawerProps) {
  const { formatMessage: t } = useIntl();
  const [price, setPrice] = useState('');
  const [creating, setCreating] = useState(false);

  const handleCreateOrder = async () => {
    if (!gift?.id || !price) return;

    const priceNumber = parseFloat(price);
    if (isNaN(priceNumber) || priceNumber <= 0) {
      toast.error('Please enter a valid price greater than 0');
      return;
    }

    setCreating(true);
    try {
      const result = await createSellOrderFromGift(gift.id, priceNumber);

      if (result.success) {
        toast.success(t(createSellOrderDrawerMessages.successMessage));
        onOrderCreated();
        onOpenChange(false);
        setPrice('');
      } else {
        toast.error(result.message || 'Failed to create sell order');
      }
    } catch (error) {
      console.error('Error creating sell order:', error);
      toast.error('Failed to create sell order');
    } finally {
      setCreating(false);
    }
  };

  if (!gift) return null;

  return (
    <BaseDrawer open={open} onOpenChange={onOpenChange}>
      <DrawerHeader title={t(createSellOrderDrawerMessages.createSellOrder)} />

      <div className="space-y-4">
        <div className="text-center">
          <p className="text-[#708499] text-sm">
            {t(createSellOrderDrawerMessages.setPrice, {
              giftName: gift.base_name,
            })}
          </p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="price" className="text-[#f5f5f5]">
            {t(createSellOrderDrawerMessages.priceLabel)}
          </Label>
          <div className="relative">
            <Input
              id="price"
              type="number"
              step="0.01"
              min="0"
              placeholder="0.00"
              value={price}
              onChange={(e) => setPrice(e.target.value)}
              className="bg-[#232e3c] border-[#3a4a5c] text-[#f5f5f5] pr-12"
              disabled={creating}
            />
            <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#708499] text-sm">
              TON
            </span>
          </div>
        </div>

        <div className="flex gap-3 pt-4">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="flex-1 border-[#3a4a5c] text-[#708499] hover:bg-[#232e3c]"
            disabled={creating}
          >
            {t(createSellOrderDrawerMessages.cancel)}
          </Button>
          <Button
            onClick={handleCreateOrder}
            disabled={!price || creating || parseFloat(price) <= 0}
            className="flex-1 bg-[#6ab2f2] hover:bg-[#5a9fd9] text-white"
          >
            {creating
              ? t(createSellOrderDrawerMessages.creating)
              : t(createSellOrderDrawerMessages.createOrder)}
          </Button>
        </div>
      </div>
    </BaseDrawer>
  );
}
