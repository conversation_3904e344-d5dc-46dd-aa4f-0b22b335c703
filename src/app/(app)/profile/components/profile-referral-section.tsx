'use client';

import { Co<PERSON>, Gift, Share, Users } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import { toast } from 'sonner';

import { getAppConfig } from '@/api/app-config-api';
import { getUserReferrals } from '@/api/user-api';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { commonMessages } from '@/intl/common.messages';
import type {
  AppConfigEntity,
  UserEntity,
} from '@/mikerudenko/marketplace-shared';
import { REFERRAL_TIERS } from '@/mikerudenko/marketplace-shared';
import { useRootContext } from '@/root-context';
import {
  formatBPSToPercent,
  getReferralFeeRate,
} from '@/services/user-service';
import { generateReferralLink } from '@/utils/referral-utils';

import { profileReferralSectionMessages } from './intl/profile-referral-section.messages';

export const ProfileReferralSection = () => {
  const { formatMessage: t } = useIntl();
  const { currentUser } = useRootContext();
  const [referrals, setReferrals] = useState<UserEntity[]>([]);
  const [appConfig, setAppConfig] = useState<AppConfigEntity | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSharing, setIsSharing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (!currentUser?.id) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        const [userReferrals, config] = await Promise.all([
          getUserReferrals(currentUser.id),
          getAppConfig(),
        ]);
        setReferrals(userReferrals);
        setAppConfig(config);
      } catch (err) {
        console.error('Error fetching referrals data:', err);
        setError(t(profileReferralSectionMessages.failedToLoadReferrals));
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [currentUser?.id]);

  if (!currentUser) {
    return null;
  }

  const referralFeeRate = getReferralFeeRate(currentUser, appConfig);
  const referralFeePercentage = formatBPSToPercent(referralFeeRate);

  const handleShareReferralLink = async () => {
    setIsSharing(true);

    const referralLink = generateReferralLink(currentUser.id);

    try {
      if (
        navigator.share &&
        /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
          navigator.userAgent,
        )
      ) {
        await navigator.share({
          title: t(profileReferralSectionMessages.joinTheMarketplace),
          text: t(profileReferralSectionMessages.joinMeOnMarketplace),
          url: referralLink,
        });
        toast.success(
          t(profileReferralSectionMessages.referralLinkSharedSuccessfully),
        );
      } else {
        await navigator.clipboard.writeText(referralLink);
        toast.success(t(commonMessages.referralLinkCopiedToClipboard));
      }
    } catch (error) {
      console.error('Error sharing referral link:', error);
      try {
        await navigator.clipboard.writeText(referralLink);
        toast.success(t(commonMessages.referralLinkCopiedToClipboard));
      } catch {
        toast.error(t(commonMessages.failedToShareReferralLink));
      }
    } finally {
      setIsSharing(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="w-5 h-5" />
          {t(profileReferralSectionMessages.referralProgram)}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="text-center py-8">
            <p className="text-[#708499]">
              {t(profileReferralSectionMessages.loadingReferralData)}
            </p>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <p className="text-red-400">{error}</p>
          </div>
        ) : (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-[#232e3c] rounded-lg p-4 border border-[#3a4a5c]">
                <h4 className="text-sm font-medium text-[#f5f5f5] mb-2">
                  {t(profileReferralSectionMessages.yourReferralRate)}
                </h4>
                <p className="text-2xl font-bold text-[#0098EA]">
                  {referralFeePercentage}%
                </p>
                <p className="text-xs text-[#708499] mt-1">
                  {t(profileReferralSectionMessages.referralRateDescription, {
                    percentage: referralFeePercentage,
                  })}
                </p>
              </div>

              <div className="bg-[#232e3c] rounded-lg p-4 border border-[#3a4a5c] flex flex-col justify-center">
                <Button
                  onClick={handleShareReferralLink}
                  disabled={isSharing}
                  className="w-full bg-[#0098EA] hover:bg-[#0088d4] text-white"
                >
                  {isSharing ? (
                    <>
                      <Copy className="w-4 h-4 mr-2 animate-spin" />
                      {t(profileReferralSectionMessages.sharing)}
                    </>
                  ) : (
                    <>
                      <Share className="w-4 h-4 mr-2" />
                      {t(profileReferralSectionMessages.shareReferralLink)}
                    </>
                  )}
                </Button>
              </div>
            </div>

            <div className="bg-[#232e3c] rounded-lg p-4 border border-[#3a4a5c]">
              <div className="flex items-center gap-2 mb-3">
                <Gift className="w-5 h-5 text-[#0098EA]" />
                <h4 className="text-sm font-medium text-[#f5f5f5]">
                  {t(profileReferralSectionMessages.shareTheLinkGetPoints)}
                </h4>
              </div>

              <div className="grid grid-cols-2 xs:grid-cols-3 sm:grid-cols-4 gap-2">
                {REFERRAL_TIERS.map((tier) => (
                  <div
                    key={`${tier.friends}-${tier.points}`}
                    className="text-center p-2 bg-[#1a2332] rounded-lg border border-[#2a3441]"
                  >
                    <div className="text-lg font-bold text-[#0098EA]">
                      {tier.friends}
                    </div>
                    <div className="text-xs text-[#708499] mb-1">
                      {t(profileReferralSectionMessages.friends)}
                    </div>
                    <div className="text-sm font-semibold text-[#f5f5f5]">
                      +{tier.points} {t(profileReferralSectionMessages.points)}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {referrals.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-[#f5f5f5] mb-3">
                  {t(profileReferralSectionMessages.yourReferrals, {
                    count: referrals.length,
                  })}
                </h4>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>
                          {t(profileReferralSectionMessages.name)}
                        </TableHead>
                        <TableHead>
                          {t(profileReferralSectionMessages.potentialEarnings)}
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {referrals.map((referral) => (
                        <TableRow key={referral.id}>
                          <TableCell>
                            {referral.displayName ||
                              referral.name ||
                              t(profileReferralSectionMessages.anonymous)}
                          </TableCell>
                          <TableCell>
                            <span className="text-[#0098EA]">
                              {referralFeePercentage}%
                            </span>
                            <span className="text-xs text-[#708499] ml-2">
                              {t(
                                profileReferralSectionMessages.ofTheirPurchaseFees,
                              )}
                            </span>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
