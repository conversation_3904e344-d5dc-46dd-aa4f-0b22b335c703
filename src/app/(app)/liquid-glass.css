/* LIQUID GLASS STYLES */
.liquidGlass-wrapper {
  position: relative;
  display: flex;
  font-weight: 600;
  overflow: hidden;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 0 40px rgba(0, 0, 0, 0.2),
    0 2px 8px rgba(0, 0, 0, 0.4);
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 2.2);
}

.liquidGlass-wrapper:hover {
  box-shadow:
    0 12px 48px rgba(0, 0, 0, 0.4),
    0 0 60px rgba(0, 0, 0, 0.3),
    0 4px 16px rgba(0, 0, 0, 0.5);
  transform: translateY(-2px);
}

.liquidGlass-effect {
  position: absolute;
  z-index: 0;
  inset: 0;
  backdrop-filter: blur(8px) saturate(180%);
  filter: url(#glass-distortion);
  overflow: hidden;
}

.liquidGlass-tint {
  z-index: 1;
  position: absolute;
  inset: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
}

.liquidGlass-shine {
  position: absolute;
  inset: 0;
  z-index: 2;
  overflow: hidden;
  box-shadow:
    inset 3px 3px 2px 0 rgba(255, 255, 255, 0.4),
    inset -2px -2px 2px 1px rgba(255, 255, 255, 0.3),
    inset 0 0 20px rgba(255, 255, 255, 0.1);
}

.liquidGlass-text {
  z-index: 3;
  color: white;
  width: 100%;
}

.footer-dock {
  border-radius: 2.5rem;
  padding: 0.8rem;
  min-height: 80px;
}

.footer-dock:hover {
  padding: 1rem;
  border-radius: 3rem;
}

.footer-dock:hover > div {
  border-radius: 3rem;
}

/* Glass boxes for individual footer items */
.footer-item-glass {
  position: relative;
  border-radius: 1.5rem;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  margin: 0.25rem;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.footer-item-glass:hover {
  background: rgba(255, 255, 255, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.25);
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transform: translateY(-2px) scale(1.05);
}

.footer-item-active {
  background: rgba(59, 130, 246, 0.15);
  border: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow:
    0 6px 20px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
}

.footer-item-active:hover {
  background: rgba(59, 130, 246, 0.2);
  border: 1px solid rgba(59, 130, 246, 0.4);
  box-shadow:
    0 10px 28px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.35);
}

/* Add glow effect for active items */
.footer-dock a.active-glow {
  position: relative;
}

.footer-dock a.active-glow::before {
  content: '';
  position: absolute;
  inset: -4px;
  background: linear-gradient(45deg, #3b82f6, #60a5fa, #93c5fd);
  border-radius: 1rem;
  opacity: 0.3;
  filter: blur(8px);
  z-index: -1;
  animation: pulse 2s infinite;
}
