/* LIQUID GLASS STYLES */
.liquidGlass-wrapper {
  position: relative;
  display: flex;
  font-weight: 600;
  overflow: hidden;
  box-shadow: 0 6px 6px rgba(0, 0, 0, 0.2), 0 0 20px rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 2.2);
}

.liquidGlass-effect {
  position: absolute;
  z-index: 0;
  inset: 0;
  backdrop-filter: blur(3px);
  filter: url(#glass-distortion);
  overflow: hidden;
}

.liquidGlass-tint {
  z-index: 1;
  position: absolute;
  inset: 0;
  background: rgba(255, 255, 255, 0.1);
}

.liquidGlass-shine {
  position: absolute;
  inset: 0;
  z-index: 2;
  overflow: hidden;
  box-shadow: inset 2px 2px 1px 0 rgba(255, 255, 255, 0.3),
    inset -1px -1px 1px 1px rgba(255, 255, 255, 0.3);
}

.liquidGlass-text {
  z-index: 3;
  color: white;
  width: 100%;
}

.footer-dock {
  border-radius: 2rem;
  padding: 0.6rem;
}

.footer-dock:hover {
  padding: 0.8rem;
  border-radius: 2.5rem;
}

.footer-dock:hover > div {
  border-radius: 2.5rem;
}
