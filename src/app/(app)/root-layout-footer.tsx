'use client';

import './liquid-glass.css';

import { Gift, ShoppingCart, Store, User } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useIntl } from 'react-intl';

import { AppRoutes } from '@/core.constants';
import { cn } from '@/lib/utils';

import { rootLayoutFooterMessages } from './intl/root-layout-footer.messages';

export default function RootLayoutFooter() {
  const { formatMessage: t } = useIntl();
  const pathname = usePathname();

  const allNavItems = [
    {
      icon: Store,
      label: t(rootLayoutFooterMessages.marketplace),
      route: AppRoutes.MARKETPLACE,
      active: pathname === AppRoutes.MARKETPLACE,
    },
    {
      icon: ShoppingCart,
      label: t(rootLayoutFooterMessages.myOrders),
      route: AppRoutes.ORDERS,
      active: pathname === AppRoutes.ORDERS,
    },
    {
      icon: Gift,
      label: t(rootLayoutFooterMessages.myGifts),
      route: AppRoutes.GIFTS,
      active: pathname === AppRoutes.GIFTS,
    },
    {
      icon: User,
      label: t(rootLayoutFooterMessages.myProfile),
      route: AppRoutes.PROFILE,
      active: pathname === AppRoutes.PROFILE,
    },
  ];

  const navItems = allNavItems;

  return (
    <>
      {/* SVG Filter for Liquid Glass Effect */}
      <svg style={{ display: 'none' }}>
        <filter
          id="glass-distortion"
          x="0%"
          y="0%"
          width="100%"
          height="100%"
          filterUnits="objectBoundingBox"
        >
          <feTurbulence
            type="fractalNoise"
            baseFrequency="0.01 0.01"
            numOctaves="1"
            seed="5"
            result="turbulence"
          />
          <feComponentTransfer in="turbulence" result="mapped">
            <feFuncR type="gamma" amplitude="1" exponent="10" offset="0.5" />
            <feFuncG type="gamma" amplitude="0" exponent="1" offset="0" />
            <feFuncB type="gamma" amplitude="0" exponent="1" offset="0.5" />
          </feComponentTransfer>
          <feGaussianBlur in="turbulence" stdDeviation="3" result="softMap" />
          <feSpecularLighting
            in="softMap"
            surfaceScale="5"
            specularConstant="1"
            specularExponent="100"
            lightingColor="white"
            result="specLight"
          >
            <fePointLight x="-200" y="-200" z="300" />
          </feSpecularLighting>
          <feComposite
            in="specLight"
            operator="arithmetic"
            k1="0"
            k2="1"
            k3="1"
            k4="0"
            result="litImage"
          />
          <feDisplacementMap
            in="SourceGraphic"
            in2="softMap"
            scale="150"
            xChannelSelector="R"
            yChannelSelector="G"
          />
        </filter>
      </svg>

      <footer className="fixed bottom-0 left-0 right-0 z-50 p-2">
        <div className="liquidGlass-wrapper footer-dock">
          <div className="liquidGlass-effect"></div>
          <div className="liquidGlass-tint"></div>
          <div className="liquidGlass-shine"></div>
          <div className="liquidGlass-text">
            <div
              className={cn(
                'flex items-center justify-between h-full footer-dock',
              )}
            >
              {navItems.map((item) => {
                const IconComponent = item.icon;
                return (
                  <Link
                    key={item.route}
                    href={item.route}
                    className={`footer-item-glass flex flex-col flex-1 items-center gap-2 p-4 pb-5 h-auto transition-all duration-500 ease-out transform hover:scale-110 ${
                      item.active
                        ? 'text-blue-400 scale-105 drop-shadow-lg footer-item-active'
                        : 'text-white hover:text-blue-200 hover:scale-110'
                    }`}
                  >
                    <div
                      className={`transition-all duration-300 ${item.active ? 'animate-pulse' : ''}`}
                    >
                      <IconComponent className="w-7 h-7" />
                    </div>
                    <span className="text-sm font-semibold leading-tight tracking-wide">
                      {item.label}
                    </span>
                  </Link>
                );
              })}
            </div>
          </div>
        </div>
      </footer>
    </>
  );
}
